<?php

if (!class_exists('WP_List_Table')) {
    require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
}

class Custom_Data_List_Table extends WP_List_Table
{

    public function __construct()
    {
        parent::__construct([
            'singular' => 'custom_data',
            'plural' => 'custom_data',
            'ajax' => false
        ]);
    }

    public function get_columns()
    {
        return [
            'cb' => '<input type="checkbox" />',
            'external_id' => 'External ID',
            'title' => 'Title',
            'url' => 'URL',
            'local' => 'Local Tour',
            'time' => 'Time'
        ];
    }

    public function single_row($item) {
        // Get the post ID from the item
        $post_id = $item['external_id'] ?? 0;

        // Start the row with a custom attribute
        echo '<tr data-external-id="' . esc_attr($post_id) . '">';

        // Call the parent method to output the columns
        $this->single_row_columns($item);

        echo '</tr>';
    }

    public function prepare_items()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'custom_external_posts_table';

        $per_page = 15;
        $columns = $this->get_columns();
        $hidden = [];
        $sortable = $this->get_sortable_columns();

        $this->_column_headers = [$columns, $hidden, $sortable];

        $current_page = $this->get_pagenum();
        $total_items = $wpdb->get_var("SELECT COUNT(id) FROM $table_name");

        $this->set_pagination_args([
            'total_items' => $total_items,
            'per_page' => $per_page
        ]);

        $this->items = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name ORDER BY time DESC LIMIT %d OFFSET %d",
                $per_page,
                ($current_page - 1) * $per_page
            ),
            ARRAY_A
        );

       /* $this->items = array_map(function ($item) {
            return array(
                'ID' => $item['id'],
                ...$item
            );
        }, $this->items);
        */
    }

    public function remove_entry($item_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'custom_external_posts_table';
        $query = $wpdb->prepare("DELETE FROM $table_name WHERE ID = %d", $item_id);
        $wpdb->query($query);
    }

    public function column_default($item, $column_name)
    {
        switch ($column_name) {
            case 'title':
            case 'url':
            case 'time':
            case 'external_id':
                return $item[$column_name];
            default:
                return print_r($item, true);
        }
    }

    public function column_local($item)
    {
        $tours = get_posts(array(
            "post_type" => "tour",
            "tour-category" => "klein-curacao",
            "numberposts" => -1,
        ));

        $select = '<select class="action-select" name="status[' . $item['id'] . ']">';
        $select .= '<option value="0">' . __('Select', 'custom-posts-sync-plugin') . '</option>';
        foreach ($tours as $tour) {

            $local_external_id = get_post_meta($tour->ID, 'external_id', true);
            // var_dump($tour->ID, $item["external_id"], $local_external_id);
            $select .= sprintf(
                '<option value="%s" %s>%s</option>',
                $tour->ID,
                selected((int)$item["external_id"], $local_external_id, false),
                $tour->post_title
            );
        }
        $select .= '</select>';

        return $select;
    }


    public function process_bulk_action() {
        if ($this->current_action() === 'delete') {
            $post_ids = isset($_POST['custom_data']) ? $_POST['custom_data'] : array();
            if (!empty($post_ids)) {
                foreach ($post_ids as $post_id) {
                    // wp_trash_post($post_id);
                    $this->remove_entry($post_id);
                }
                add_action('admin_notices', array($this, 'bulk_action_notice'));
            }
        }
    }


    public function bulk_action_notice() {
        echo '<div class="updated"><p>Tours Link removed.</p></div>';
    }

    public function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" name="link_posts[]" value="%s" />',
            $item['id']
        );
    }

    public function get_sortable_columns()
    {
        return [
            'name' => ['name', true],
            'time' => ['time', true]
        ];
    }


}

add_action('admin_footer', 'add_select_all_checkbox_script');

function add_select_all_checkbox_script() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.tablenav.top .bulkactions').prepend('<input type="checkbox" id="select-all-checkbox" />Select All</input>');
            $('#select-all-checkbox').click(function(event) {
                if(this.checked) {
                    $(':checkbox').each(function() {
                        this.checked = true;
                    });
                } else {
                    $(':checkbox').each(function() {
                        this.checked = false;
                    });
                }
            });
        });
    </script>
    <?php
}