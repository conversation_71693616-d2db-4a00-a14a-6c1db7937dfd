<?php
/**
 * Custom Posts Sync Plugin
 *
 * @package           Custom_Posts_Sync
 * <AUTHOR>
 * @copyright         2024 DevPLus31
 *
 * @wordpress-plugin
 * Plugin Name:       Custom Posts Sync Plugin
 * Description:       Sync Posts ID from external website.
 * Author:            DevPlus31
 * Version:           1.0
 * Text Domain:       Custom Posts Sync Plugin
 */


class Custom_Posts_Sync_Plugin {


    private $api_endpoint = "https://caribbean-tours.nl/wp-json/wp/v2/";

    // private $api_endpoint = "http://*************/wp-json/wp/v2/";

    public $updateMeta = false;

    public function __construct()
    {
    }



    public function fetch_tours() {
        // Validate API endpoint
        if (empty($this->api_endpoint) || !filter_var($this->api_endpoint, FILTER_VALIDATE_URL)) {
            error_log('Custom Posts Sync: Invalid API endpoint configured');
            return false;
        }

        $request_bestemming = wp_remote_get($this->api_endpoint . 'tour?bestemming=142&per_page=100', array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'Custom-Posts-Sync-Plugin/1.0'
            )
        ));

        $request_category = wp_remote_get($this->api_endpoint . 'tour?tour_category=138&per_page=100', array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'Custom-Posts-Sync-Plugin/1.0'
            )
        ));

        if (is_wp_error($request_bestemming)) {
            error_log('Custom Posts Sync: API request failed for bestemming: ' . $request_bestemming->get_error_message());
            return false;
        }

        if (is_wp_error($request_category)) {
            error_log('Custom Posts Sync: API request failed for category: ' . $request_category->get_error_message());
            return false;
        }

        // Validate HTTP response codes
        $response_code_bestemming = wp_remote_retrieve_response_code($request_bestemming);
        $response_code_category = wp_remote_retrieve_response_code($request_category);

        if ($response_code_bestemming !== 200 || $response_code_category !== 200) {
            error_log("Custom Posts Sync: API returned non-200 response codes: $response_code_bestemming, $response_code_category");
            return false;
        }

        $body_bestemming = wp_remote_retrieve_body($request_bestemming);
        $body_category = wp_remote_retrieve_body($request_category);

        // Validate response bodies
        if (empty($body_bestemming) || empty($body_category)) {
            error_log('Custom Posts Sync: Empty response body received from API');
            return false;
        }

        $tours_destination = json_decode($body_bestemming);
        $tours_category = json_decode($body_category);

        // Validate JSON decode
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('Custom Posts Sync: JSON decode error: ' . json_last_error_msg());
            return false;
        }

        // Validate that we received arrays
        if (!is_array($tours_destination) || !is_array($tours_category)) {
            error_log('Custom Posts Sync: API response is not in expected array format');
            return false;
        }

        $tours = array_merge($tours_category, $tours_destination);

        $unique_tours = [];
        $ids = [];

        foreach ($tours as $tour) {
            // Validate tour object structure
            if (!is_object($tour) || !isset($tour->id) || !isset($tour->title) || !isset($tour->link)) {
                error_log('Custom Posts Sync: Invalid tour object structure received');
                continue;
            }

            // Validate tour ID is numeric
            if (!is_numeric($tour->id) || $tour->id <= 0) {
                error_log('Custom Posts Sync: Invalid tour ID: ' . $tour->id);
                continue;
            }

            // Sanitize tour data
            $tour->id = absint($tour->id);
            $tour->link = esc_url_raw($tour->link);

            if (isset($tour->title->rendered)) {
                $tour->title->rendered = sanitize_text_field($tour->title->rendered);
            }

            if (!in_array($tour->id, $ids)) {
                $ids[] = $tour->id;
                $unique_tours[] = $tour;
            }
        }

        if (!empty($unique_tours)) {
            error_log('Custom Posts Sync: Successfully fetched ' . count($unique_tours) . ' tours');
            return $unique_tours;
        }

        error_log('Custom Posts Sync: No valid tours found in API response');
        return false;
    }

    public function sync_tours() {
        $tours = $this->fetch_tours();
        if (! empty($tours)) {
            foreach ($tours as $tour) {
                $tour_meta = $tour->metadata;
                insert_into_table($tour->title->rendered, $tour->id, $tour->link, $tour_meta, $this->updateMeta);
            }
        }
    }

    function fetch_tour($id) {
        // Validate input
        if (!is_numeric($id) || $id <= 0) {
            error_log('Custom Posts Sync: Invalid tour ID provided: ' . $id);
            return false;
        }

        $id = absint($id);

        // Validate API endpoint
        if (empty($this->api_endpoint) || !filter_var($this->api_endpoint, FILTER_VALIDATE_URL)) {
            error_log('Custom Posts Sync: Invalid API endpoint configured');
            return false;
        }

        $request = wp_remote_get($this->api_endpoint . 'tour/' . $id, array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'Custom-Posts-Sync-Plugin/1.0'
            )
        ));

        if (is_wp_error($request)) {
            error_log('Custom Posts Sync: API request failed for tour ID ' . $id . ': ' . $request->get_error_message());
            return false;
        }

        // Validate HTTP response code
        $response_code = wp_remote_retrieve_response_code($request);
        if ($response_code !== 200) {
            error_log('Custom Posts Sync: API returned non-200 response code for tour ID ' . $id . ': ' . $response_code);
            return false;
        }

        $body = wp_remote_retrieve_body($request);

        // Validate response body
        if (empty($body)) {
            error_log('Custom Posts Sync: Empty response body for tour ID: ' . $id);
            return false;
        }

        $tour = json_decode($body, true);

        // Validate JSON decode
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('Custom Posts Sync: JSON decode error for tour ID ' . $id . ': ' . json_last_error_msg());
            return false;
        }

        // Validate tour data structure
        if (!is_array($tour) || empty($tour)) {
            error_log('Custom Posts Sync: Invalid or empty tour data for ID: ' . $id);
            return false;
        }

        // Basic validation of required fields
        $required_fields = array('id', 'title', 'link');
        foreach ($required_fields as $field) {
            if (!isset($tour[$field])) {
                error_log('Custom Posts Sync: Missing required field "' . $field . '" in tour data for ID: ' . $id);
                return false;
            }
        }

        // Sanitize tour data
        if (isset($tour['id'])) {
            $tour['id'] = absint($tour['id']);
        }
        if (isset($tour['link'])) {
            $tour['link'] = esc_url_raw($tour['link']);
        }
        if (isset($tour['title']['rendered'])) {
            $tour['title']['rendered'] = sanitize_text_field($tour['title']['rendered']);
        }

        error_log('Custom Posts Sync: Successfully fetched tour ID: ' . $id);
        return $tour;
    }
}


function sync_tours_options($options, $local_tour_id) {

    $local_tour_options = tourmaster_get_post_meta($local_tour_id, 'tourmaster-tour-option'); // get_post_meta($local_tour_id, 'tourmaster-tour-option', true);
    $date_price =  $local_tour_options['date-price'];
    $package_price =  $local_tour_options['package'];


    foreach ($date_price as $key => &$value) {
        $value['exclude-extra-date'] =  $options['exclude-extra-date'];
        $value['date'] = $options['date-price'][0]['date'];
        $value['day'] = $options['date-price'][0]['day'];
        $value['month'] = $options['date-price'][0]['month'];
        $value['year'] = $options['date-price'][0]['year'];
    }

    foreach ($package_price as $key => &$value) {
        $value['adult-price'] =  $options['adult-price-second'];
        $value['children-price'] =  $options['children-price-second'];
        $value['infant-price'] =  $options['infant-price-second'];
    }


    $local_tour_options['date-price'] = $date_price;
    $local_tour_options['package'] = $package_price;

    $local_tour_options['tour-price-text'] = $options['tour-price-text-second'];
    $local_tour_options['last-minute-booking'] = $options['last-minute-booking'];

    $local_tour_options['available_from'] = $options['available_from'];

    update_post_meta($local_tour_id, 'tourmaster-tour-option', $local_tour_options);

    // Code copy. Plugin do some process to set available dates.
    if( !empty($local_tour_options['date-price']) && !empty($local_tour_options['tour-timing-method']) ){
        $date_list = array();
        foreach( $local_tour_options['date-price'] as $settings ){
            $dates = tourmaster_get_tour_dates($settings, $local_tour_options['tour-timing-method']);
            $date_list = array_merge($date_list, $dates);
        }

        if( !empty($date_list) ){
            $date_list = array_unique($date_list);
            sort($date_list);
            update_post_meta($local_tour_id, 'tourmaster-tour-date', implode(',', $date_list));

            $book_in_advance = empty($local_tour_options['book-in-advance'])? '': $local_tour_options['book-in-advance'];
            $date_avail = tourmaster_filter_tour_date($date_list, $book_in_advance);
            if( !empty($date_avail) ){
                update_post_meta($local_tour_id, 'tourmaster-tour-date-avail', implode(',', $date_avail));
            }else{
                delete_post_meta($local_tour_id, 'tourmaster-tour-date-avail');
            }
        }else{
            delete_post_meta($local_tour_id, 'tourmaster-tour-date');
            delete_post_meta($local_tour_id, 'tourmaster-tour-date-avail');
        }
    }else{
        delete_post_meta($local_tour_id, 'tourmaster-tour-date');
        delete_post_meta($local_tour_id, 'tourmaster-tour-date-avail');
    }

}
function insert_into_table($title, $external_id, $url, $meta_data, $updateMeta = false): bool|int
{
    global $wpdb;

    // Validate and sanitize inputs
    if (empty($title) || !is_string($title)) {
        error_log('Custom Posts Sync: Invalid title provided to insert_into_table');
        return false;
    }

    if (!is_numeric($external_id) || $external_id <= 0) {
        error_log('Custom Posts Sync: Invalid external_id provided to insert_into_table: ' . $external_id);
        return false;
    }

    if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
        error_log('Custom Posts Sync: Invalid URL provided to insert_into_table: ' . $url);
        return false;
    }

    // Sanitize inputs
    $title = sanitize_text_field($title);
    $external_id = absint($external_id);
    $url = esc_url_raw($url);
    $updateMeta = (bool) $updateMeta;

    // Limit title length to prevent database issues
    if (strlen($title) > 255) {
        $title = substr($title, 0, 255);
        error_log('Custom Posts Sync: Title truncated for external_id ' . $external_id);
    }

    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    // Check if record already exists
    $existing_record = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE external_id = %d LIMIT 1",
            $external_id
        )
    );

    if ($existing_record) {
        if ($updateMeta) {
            update_existing_tours_metadata($external_id, $meta_data);
        }
        return true;
    }

    // Insert new record
    $result = $wpdb->insert(
        $table_name,
        array(
            'title' => $title,
            'external_id' => $external_id,
            'url' => $url
        ),
        array('%s', '%d', '%s')
    );

    if ($result === false) {
        error_log('Custom Posts Sync: Database insert failed: ' . $wpdb->last_error);
        return false;
    }

    // Validate metadata before processing
    if ($meta_data !== null && !is_object($meta_data) && !is_array($meta_data)) {
        error_log('Custom Posts Sync: Invalid metadata format for external_id ' . $external_id);
        $meta_data = null;
    }

    sync_tours_names_metadata($external_id, $meta_data);

    $insert_id = $wpdb->insert_id;
    error_log('Custom Posts Sync: Successfully inserted tour with ID: ' . $insert_id . ', external_id: ' . $external_id);

    return $insert_id;
}

function hitCronJob() {
   error_log('Cronjob External sync started!');
   $inst = new  Custom_Posts_Sync_Plugin();
   $inst->updateMeta = true;
   $inst->sync_tours();
   error_log('Cronjob: External tours sync finished');
}

function update_existing_tours_metadata($external_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    $tours = get_posts(array(
        "post_type" => "tour",
        "tour-category" => "klein-curacao",
        "numberposts" => -1,
    ));

    $item = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE external_id = %d LIMIT 1",
            $external_id
        )
    );

    $instance = new Custom_Posts_Sync_Plugin();
    $instance->updateMeta = true;
    if ($item == null) { return false; }
    // $meta_data = (array)$meta_data;

    foreach ($tours as $tour) {
        $local_external_id = get_post_meta($tour->ID, 'external_id', true);
        if ( (int)$local_external_id === (int)$external_id) {
            $ftour = $instance->fetch_tour($item->external_id);
            sync_tours_options($ftour, $tour->ID);
            break;
        }
    }
}

function sync_tours_names_metadata($id, $meta_data) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    // Only get tours that don't have an external_id assigned yet AND are not manually assigned
    $tours = get_posts(array(
        "post_type" => "tour",
        "tour-category" => "klein-curacao",
        "numberposts" => -1,
        "meta_query" => array(
            'relation' => 'AND',
            array(
                'relation' => 'OR',
                array(
                    'key' => 'external_id',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key' => 'external_id',
                    'value' => '',
                    'compare' => '='
                ),
                array(
                    'key' => 'external_id',
                    'value' => null,
                    'compare' => '='
                )
            ),
            array(
                'key' => 'external_id_manual',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    $item = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE external_id = %d LIMIT 1",
            $id
        )
    );

    if ($item == null) { return false; }

    // Check if this external_id is already assigned to another tour
    $already_assigned = get_posts(array(
        "post_type" => "tour",
        "numberposts" => 1,
        "meta_query" => array(
            array(
                'key' => 'external_id',
                'value' => $item->external_id,
                'compare' => '='
            )
        )
    ));

    // If already assigned, skip this external tour
    if (!empty($already_assigned)) {
        error_log('Custom Posts Sync: External ID ' . $item->external_id . ' already assigned to post ID ' . $already_assigned[0]->ID);
        return false;
    }

    foreach( $tours as $tour ) {
        // Don't reset - only assign if not already assigned
        $similarity = 0;
        $title_to_compare = $tour->post_title; // Use post_title for local posts

        similar_text($title_to_compare, $item->title, $similarity);

        if ($similarity > 80) {
            sync_external_id_to_meta($tour->ID, $item->external_id);
            sync_tours_options($meta_data, $tour->ID);
            error_log('Custom Posts Sync: Auto-matched tour "' . $title_to_compare . '" with external "' . $item->title . '" (similarity: ' . $similarity . '%)');
            break; // Only match to one tour
        }
    }
}

function devplus31_posts_sync_start_plugin()
{
    $instance = new Custom_Posts_Sync_Plugin();
    $instance->sync_tours();
}

function custom_data_menu() {
    add_menu_page(
        'External Tours',
        'External Tours',
        'manage_options',
        'external-tours-data',
        'external_tours_page'
    );
}

function custom_sync_cronjob() {

    if (!wp_next_scheduled( 'custom_tourmaster_sync_run_cron')) {
        wp_schedule_event(time(), 'hourly', 'custom_tourmaster_sync_run_cron');
    }
}

function custom_sync_deactivate_scheduler() {
    wp_clear_scheduled_hook( 'custom_sync_cronjob' );
}

add_action('admin_menu', 'custom_data_menu');
add_action('admin_footer', 'add_action_select_script');

register_activation_hook(__FILE__, 'custom_sync_cronjob');
register_deactivation_hook(__FILE__, 'custom_sync_deactivate_scheduler');

add_action('custom_tourmaster_sync_run_cron', function() {
   hitCronJob();
});

function add_action_select_script() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {

            $('.action-select').attr("old-local-id", $('.action-select option:selected').val() )

            $('.action-select').change(function() {
                var base = this;
                var action = $(this).val();
                var itemId = $(this).data('item-id');
                var oldLocalId = $(this).attr('old-local-id');
                var externalId = $(this).parents("tr").data("external-id");
                if (action) {
                    var url = '<?php echo admin_url('admin-ajax.php'); ?>';
                    $.post(url, {
                        action: 'perform_list_table_action',
                        list_action: action,
                        old_local_id: oldLocalId,
                        item_id: itemId,
                        external_id: externalId,
                        _ajax_nonce: '<?php echo wp_create_nonce('list_table_action'); ?>'
                    }, function(response) {
                        alert(response);

                        $(base).attr("old-local-id", $('.action-select option:selected').val() )
                        // Reset select
                        // $('.action-select').val('');
                    });
                }
            });
        });
    </script>
    <?php
}


add_action('admin_footer', 'add_custom_bulk_action_script');

function add_custom_bulk_action_script() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#remove-selected').click(function(e) {
                e.preventDefault();

                var selectedIds = [];
                $('input[name="link_posts[]"]:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                if (selectedIds.length === 0) {
                    alert('Please select items to remove.');
                    return;
                }

                if (confirm('Are you sure you want to remove the selected items?')) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'remove_selected_items',
                            post_ids: selectedIds,
                            _ajax_nonce: '<?php echo wp_create_nonce('remove_selected_items'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                selectedIds.forEach(function(id) {
                                    $('tr[data-post-id="' + id + '"]').fadeOut(400, function() {
                                        $(this).remove();
                                    });
                                });
                                alert('Selected items removed successfully.');
                                window.location.reload();
                            } else {
                                alert('Error: ' + response.data);
                            }
                        },
                        error: function() {
                            alert('An error occurred while processing your request.');
                        }
                    });
                }
            });


            $('#sync-selected').click(function(e) {
                e.preventDefault();

                var selectedIds = [];
                var selectedLocalIds = []
                $('input[name="link_posts[]"]:checked').each(function() {
                    selectedIds.push($(this).val());
                    selectedLocalIds.push(jQuery(this).parents("tr").find('.action-select option:selected').get(0).value);
                });

                if (selectedIds.length === 0) {
                    alert('Please select items to sync.');
                    return;
                }

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'sync_selected_items',
                        post_ids: selectedIds,
                        selectedIds: selectedLocalIds,
                        _ajax_nonce: '<?php echo wp_create_nonce('sync_selected_items'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            selectedIds.forEach(function(id) {
                                $('tr[data-post-id="' + id + '"]').fadeOut(400, function() {
                                    $(this).remove();
                                });
                            });
                            alert('Selected items successfully synchronized!');
                            // window.location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('An error occurred while processing your request.');
                    }
                });
            });

            $('#auto-match-unassigned').click(function(e) {
                e.preventDefault();

                if (!confirm('This will automatically match unassigned tours based on title similarity. Continue?')) {
                    return;
                }

                $(this).prop('disabled', true).text('Processing...');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'auto_match_unassigned_tours',
                        _ajax_nonce: '<?php echo wp_create_nonce('auto_match_unassigned_tours'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Auto-matching completed: ' + response.data);
                            window.location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('An error occurred while processing your request.');
                    },
                    complete: function() {
                        $('#auto-match-unassigned').prop('disabled', false).text('Auto-Match Unassigned Tours');
                    }
                });
            });
        });
    </script>
    <?php
}

add_action('wp_ajax_remove_selected_items', 'handle_remove_selected_items');
add_action('wp_ajax_sync_selected_items', 'handle_sync_selected_items');
add_action('wp_ajax_auto_match_unassigned_tours', 'handle_auto_match_unassigned_tours');

function handle_sync_selected_items() {
    check_ajax_referer('sync_selected_items', '_ajax_nonce');

    if (!current_user_can('edit_posts')) {
        wp_send_json_error('You do not have permission to sync posts.');
        return;
    }

    // Validate and sanitize inputs
    $post_ids = isset($_POST['post_ids']) ? array_map('absint', $_POST['post_ids']) : array();
    $selectedIds = isset($_POST['selectedIds']) ? array_map('absint', $_POST['selectedIds']) : array();

    // Remove any zero or negative values
    $post_ids = array_filter($post_ids, function($id) { return $id > 0; });
    $selectedIds = array_filter($selectedIds, function($id) { return $id > 0; });

    if (empty($post_ids)) {
        wp_send_json_error('No valid items selected.');
        return;
    }

    if (count($post_ids) !== count($selectedIds)) {
        wp_send_json_error('Mismatch between selected items and target posts.');
        return;
    }

    $instance = new Custom_Posts_Sync_Plugin();
    $sync = 0;
    $errors = array();

    foreach ($post_ids as $index => $item_id) {
        // Validate item exists in our database
        $item = get_item_by_id($item_id);
        if (is_wp_error($item)) {
            $errors[] = "Item ID $item_id not found.";
            continue;
        }

        // Validate target post exists
        $target_post_id = $selectedIds[$index];
        if (!get_post($target_post_id)) {
            $errors[] = "Target post ID $target_post_id not found.";
            continue;
        }

        // Fetch tour data from external API
        $tour = $instance->fetch_tour($item->external_id);
        if ($tour === false) {
            $errors[] = "Failed to fetch tour data for external ID {$item->external_id}.";
            continue;
        }

        // Validate tour data structure
        if (!is_array($tour) || empty($tour)) {
            $errors[] = "Invalid tour data received for external ID {$item->external_id}.";
            continue;
        }

        sync_tours_options($tour, $target_post_id);
        $sync++;
    }

    if ($sync > 0) {
        $message = "Successfully synchronized $sync item(s).";
        if (!empty($errors)) {
            $message .= " Errors: " . implode(' ', $errors);
        }
        wp_send_json_success($message);
    } else {
        wp_send_json_error('Failed to sync any items. Errors: ' . implode(' ', $errors));
    }
}

function handle_remove_selected_items() {
    check_ajax_referer('remove_selected_items', '_ajax_nonce');

    if (!current_user_can('delete_posts')) {
        wp_send_json_error('You do not have permission to delete tours.');
        return;
    }

    // Validate and sanitize inputs
    $post_ids = isset($_POST['post_ids']) ? array_map('absint', $_POST['post_ids']) : array();

    // Remove any zero or negative values
    $post_ids = array_filter($post_ids, function($id) { return $id > 0; });

    if (empty($post_ids)) {
        wp_send_json_error('No valid items selected.');
        return;
    }

    // Limit the number of items that can be deleted at once to prevent abuse
    if (count($post_ids) > 100) {
        wp_send_json_error('Too many items selected. Please select 100 or fewer items.');
        return;
    }

    $removed = 0;
    $errors = array();

    foreach ($post_ids as $post_id) {
        // Verify the item exists before attempting to delete
        $item = get_item_by_id($post_id);
        if (is_wp_error($item)) {
            $errors[] = "Item ID $post_id not found.";
            continue;
        }

        $result = remove_item_by_id($post_id);
        if (is_wp_error($result)) {
            $errors[] = "Failed to remove item ID $post_id: " . $result->get_error_message();
        } else {
            $removed++;
        }
    }

    if ($removed > 0) {
        $message = "Successfully removed $removed item(s).";
        if (!empty($errors)) {
            $message .= " Errors: " . implode(' ', $errors);
        }
        wp_send_json_success($message);
    } else {
        wp_send_json_error('Failed to remove any items. Errors: ' . implode(' ', $errors));
    }
}

function get_item_by_id(int $item_id) {
    global $wpdb;

    // Validate input
    if ($item_id <= 0) {
        return new WP_Error('invalid_id', 'Invalid item ID provided.');
    }

    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    $query = $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $item_id);
    $result = $wpdb->get_row($query);

    if ($result === null) {
        return new WP_Error('item_not_found', 'Item not found.');
    }

    if ($result === false) {
        error_log('Custom Posts Sync: Database error in get_item_by_id: ' . $wpdb->last_error);
        return new WP_Error('fetch_failed', 'Database error occurred while fetching item.');
    }

    // Validate and sanitize the returned data
    if (isset($result->title)) {
        $result->title = sanitize_text_field($result->title);
    }
    if (isset($result->url)) {
        $result->url = esc_url_raw($result->url);
    }
    if (isset($result->external_id)) {
        $result->external_id = absint($result->external_id);
    }

    return $result;
}

function remove_item_by_id($item_id) {
    global $wpdb;

    // Validate input
    if (!is_numeric($item_id) || $item_id <= 0) {
        return new WP_Error('invalid_id', 'Invalid item ID provided.');
    }

    $item_id = absint($item_id);
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    // First check if the item exists
    $existing_item = $wpdb->get_row(
        $wpdb->prepare("SELECT id FROM $table_name WHERE id = %d", $item_id)
    );

    if (!$existing_item) {
        return new WP_Error('item_not_found', 'Item not found.');
    }

    // Perform the deletion
    $query = $wpdb->prepare("DELETE FROM $table_name WHERE id = %d", $item_id);
    $result = $wpdb->query($query);

    if ($result === false) {
        error_log('Custom Posts Sync: Database error in remove_item_by_id: ' . $wpdb->last_error);
        return new WP_Error('delete_failed', 'Database error occurred while deleting item.');
    }

    if ($result === 0) {
        return new WP_Error('delete_failed', 'No rows were deleted.');
    }

    error_log('Custom Posts Sync: Successfully deleted item ID: ' . $item_id);
    return true;
}


add_action('wp_ajax_perform_list_table_action', 'handle_list_table_action');

function handle_auto_match_unassigned_tours() {
    check_ajax_referer('auto_match_unassigned_tours', '_ajax_nonce');

    if (!current_user_can('edit_posts')) {
        wp_send_json_error('You do not have permission to perform this action.');
        return;
    }

    // Get all external tours from our custom table
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    $external_tours = $wpdb->get_results("SELECT * FROM $table_name ORDER BY id ASC");

    if (empty($external_tours)) {
        wp_send_json_error('No external tours found in database.');
        return;
    }

    $matched_count = 0;
    $skipped_count = 0;
    $errors = array();

    foreach ($external_tours as $external_tour) {
        // Check if this external_id is already assigned
        $already_assigned = get_posts(array(
            "post_type" => "tour",
            "numberposts" => 1,
            "meta_query" => array(
                array(
                    'key' => 'external_id',
                    'value' => $external_tour->external_id,
                    'compare' => '='
                )
            )
        ));

        if (!empty($already_assigned)) {
            $skipped_count++;
            continue;
        }

        // Get unassigned local tours (not manually assigned)
        $unassigned_tours = get_posts(array(
            "post_type" => "tour",
            "tour-category" => "klein-curacao",
            "numberposts" => -1,
            "meta_query" => array(
                'relation' => 'AND',
                array(
                    'relation' => 'OR',
                    array(
                        'key' => 'external_id',
                        'compare' => 'NOT EXISTS'
                    ),
                    array(
                        'key' => 'external_id',
                        'value' => '',
                        'compare' => '='
                    ),
                    array(
                        'key' => 'external_id',
                        'value' => null,
                        'compare' => '='
                    )
                ),
                array(
                    'key' => 'external_id_manual',
                    'compare' => 'NOT EXISTS'
                )
            )
        ));

        if (empty($unassigned_tours)) {
            continue;
        }

        // Find best match based on title similarity
        $best_match = null;
        $best_similarity = 0;

        foreach ($unassigned_tours as $local_tour) {
            $similarity = 0;
            similar_text($local_tour->post_title, $external_tour->title, $similarity);

            if ($similarity > $best_similarity && $similarity > 80) {
                $best_similarity = $similarity;
                $best_match = $local_tour;
            }
        }

        if ($best_match) {
            // Assign the match
            $result = sync_external_id_to_meta($best_match->ID, $external_tour->external_id, false);

            if ($result) {
                $matched_count++;
                error_log('Custom Posts Sync: Auto-matched "' . $best_match->post_title . '" with "' . $external_tour->title . '" (similarity: ' . $best_similarity . '%)');

                // Fetch and sync tour options
                $instance = new Custom_Posts_Sync_Plugin();
                $tour_data = $instance->fetch_tour($external_tour->external_id);
                if ($tour_data !== false) {
                    sync_tours_options($tour_data, $best_match->ID);
                }
            } else {
                $errors[] = "Failed to assign external ID {$external_tour->external_id} to post {$best_match->ID}";
            }
        }
    }

    $message = "Auto-matching completed. Matched: $matched_count tours. Skipped (already assigned): $skipped_count tours.";

    if (!empty($errors)) {
        $message .= " Errors: " . implode(', ', $errors);
    }

    if ($matched_count > 0 || $skipped_count > 0) {
        wp_send_json_success($message);
    } else {
        wp_send_json_error('No tours were matched. ' . $message);
    }
}

function handle_list_table_action() {
    // Verify nonce and user capabilities
    check_ajax_referer('list_table_action', '_ajax_nonce');

    if (!current_user_can('edit_posts')) {
        wp_send_json_error('You do not have permission to perform this action.');
        return;
    }

    // Validate and sanitize inputs
    $action = isset($_POST['action']) ? sanitize_text_field($_POST['action']) : '';
    $item_id = isset($_POST['list_action']) ? absint($_POST['list_action']) : 0;
    $oldLocalId = isset($_POST['old_local_id']) ? absint($_POST['old_local_id']) : 0;
    $external_id = isset($_POST['external_id']) ? absint($_POST['external_id']) : 0;

    // Validate required fields
    if (empty($action)) {
        wp_send_json_error('Invalid action specified.');
        return;
    }

    if ($external_id <= 0) {
        wp_send_json_error('Invalid external ID.');
        return;
    }

    $instance = new Custom_Posts_Sync_Plugin();

    // Perform your action based on $action and $item_id
    switch ($action) {
        case 'perform_list_table_action':
            if ($item_id === 0) {
                if ($oldLocalId > 0) {
                    reset_external_id($oldLocalId);
                    $result = "External ID reset successfully!";
                } else {
                    $result = "No action needed.";
                }
                break;
            }

            // Validate that the item_id corresponds to a valid post
            if ($item_id > 0 && !get_post($item_id)) {
                wp_send_json_error('Invalid post ID specified.');
                return;
            }

            if ($oldLocalId > 0) {
                reset_external_id($oldLocalId);
            }

            // Mark as manual assignment to prevent cron override
            sync_external_id_to_meta($item_id, $external_id, true);
            $tour = $instance->fetch_tour($external_id);

            if ($tour === false) {
                wp_send_json_error('Failed to fetch tour data from external API.');
                return;
            }

            sync_tours_options($tour, $item_id);
            $result = "Tour synchronized successfully!";
            break;
        default:
            wp_send_json_error('Unknown action specified.');
            return;
    }

    wp_send_json_success($result);
}

function external_tours_page() {
    require_once(plugin_dir_path(__FILE__) . 'class/custom-data-list-table.php');
    $table = new Custom_Data_List_Table();
    $table->prepare_items();

    // Get statistics for unassigned tours
    $stats = get_unassigned_tours_stats();
    ?>
    <style>
        .auto-match-button {
            margin-left: 10px;
            position: relative;
        }
        .auto-match-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .tours-stats {
            margin: 10px 0;
            padding: 10px;
            background: #f1f1f1;
            border-left: 4px solid #0073aa;
        }
    </style>
    <div class="wrap">
        <h1 class="wp-heading-inline">Custom Data</h1>
        <div style="margin: 10px 0;">
            <button id="remove-selected" class="button action">Remove Selected</button>
            <button id="sync-selected" class="button action">Sync Selected</button>
            <button id="auto-match-unassigned" class="button button-primary auto-match-button">
                🔄 Auto-Match Unassigned Tours
                <?php if ($stats['unassigned_local'] > 0 && $stats['unassigned_external'] > 0): ?>
                    <small>(<?php echo $stats['unassigned_local']; ?> local, <?php echo $stats['unassigned_external']; ?> external)</small>
                <?php endif; ?>
            </button>
        </div>

        <?php if ($stats['unassigned_local'] == 0 && $stats['unassigned_external'] == 0): ?>
            <p class="notice notice-info"><strong>All tours are currently assigned.</strong></p>
        <?php elseif ($stats['unassigned_local'] == 0): ?>
            <p class="notice notice-warning"><strong>No unassigned local tours available for matching.</strong></p>
        <?php elseif ($stats['unassigned_external'] == 0): ?>
            <p class="notice notice-warning"><strong>No unassigned external tours available for matching.</strong></p>
        <?php endif; ?>

        <form method="post">
            <?php
            $table->display();
            ?>
        </form>

    </div>
    <?php
}

function get_unassigned_tours_stats() {
    global $wpdb;

    // Count unassigned local tours
    $unassigned_local = get_posts(array(
        "post_type" => "tour",
        "tour-category" => "klein-curacao",
        "numberposts" => -1,
        "fields" => "ids",
        "meta_query" => array(
            'relation' => 'AND',
            array(
                'relation' => 'OR',
                array(
                    'key' => 'external_id',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key' => 'external_id',
                    'value' => '',
                    'compare' => '='
                ),
                array(
                    'key' => 'external_id',
                    'value' => null,
                    'compare' => '='
                )
            ),
            array(
                'key' => 'external_id_manual',
                'compare' => 'NOT EXISTS'
            )
        )
    ));

    // Count unassigned external tours
    $table_name = $wpdb->prefix . 'custom_external_posts_table';
    $assigned_external_ids = $wpdb->get_col(
        "SELECT DISTINCT pm.meta_value
         FROM {$wpdb->postmeta} pm
         WHERE pm.meta_key = 'external_id'
         AND pm.meta_value IS NOT NULL
         AND pm.meta_value != ''"
    );

    $total_external = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    $unassigned_external = $total_external - count($assigned_external_ids);

    return array(
        'unassigned_local' => count($unassigned_local),
        'unassigned_external' => max(0, $unassigned_external),
        'total_local' => wp_count_posts('tour')->publish ?? 0,
        'total_external' => $total_external
    );
}


function reset_external_id($post_id) {
    // Validate input
    if (!is_numeric($post_id) || $post_id <= 0) {
        error_log('Custom Posts Sync: Invalid post_id provided to reset_external_id: ' . $post_id);
        return false;
    }

    $post_id = absint($post_id);

    // Verify post exists
    if (!get_post($post_id)) {
        error_log('Custom Posts Sync: Post not found for ID: ' . $post_id);
        return false;
    }

    // Remove both external_id and manual flag
    $result1 = update_post_meta($post_id, 'external_id', null);
    $result2 = delete_post_meta($post_id, 'external_id_manual');

    if ($result1) {
        error_log('Custom Posts Sync: Successfully reset external_id and manual flag for post ID: ' . $post_id);
    }

    return $result1;
}

function sync_external_id_to_meta($post_id, $external_id, $is_manual = false) {
    // Validate inputs
    if (!is_numeric($post_id) || $post_id <= 0) {
        error_log('Custom Posts Sync: Invalid post_id provided to sync_external_id_to_meta: ' . $post_id);
        return false;
    }

    if (!is_numeric($external_id) || $external_id <= 0) {
        error_log('Custom Posts Sync: Invalid external_id provided to sync_external_id_to_meta: ' . $external_id);
        return false;
    }

    $post_id = absint($post_id);
    $external_id = absint($external_id);

    // Verify post exists
    if (!get_post($post_id)) {
        error_log('Custom Posts Sync: Post not found for ID: ' . $post_id);
        return false;
    }

    $result = update_post_meta($post_id, 'external_id', $external_id);

    // Track if this was manually assigned (to prevent cron from overriding)
    if ($is_manual) {
        update_post_meta($post_id, 'external_id_manual', true);
    }

    if ($result) {
        $assignment_type = $is_manual ? 'manually' : 'automatically';
        error_log('Custom Posts Sync: Successfully synced external_id ' . $external_id . ' to post ID: ' . $post_id . ' (' . $assignment_type . ')');
    }

    return $result;
}
function create_custom_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';
    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        time datetime DEFAULT now() NOT NULL,
        title tinytext NOT NULL,
        external_id mediumint(9) NOT NULL,
        url varchar(1024) DEFAULT '' NOT NULL,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
register_activation_hook(__FILE__, 'create_custom_table');


add_action("init", "devplus31_posts_sync_start_plugin");