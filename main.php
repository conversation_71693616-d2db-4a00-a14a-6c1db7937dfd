<?php
/**
 * Custom Posts Sync Plugin
 *
 * @package           Custom_Posts_Sync
 * <AUTHOR>
 * @copyright         2024 DevPLus31
 *
 * @wordpress-plugin
 * Plugin Name:       Custom Posts Sync Plugin
 * Description:       Sync Posts ID from external website.
 * Author:            DevPlus31
 * Version:           1.0
 * Text Domain:       Custom Posts Sync Plugin
 */


class Custom_Posts_Sync_Plugin {


    private $api_endpoint = "https://caribbean-tours.nl/wp-json/wp/v2/";

    // private $api_endpoint = "http://*************/wp-json/wp/v2/";

    public $updateMeta = false;

    public function __construct()
    {
    }



    public function fetch_tours() {
        // $request =  wp_remote_get( $this->api_endpoint . 'tour?tour_category=138' );
        $request_bestemming =  wp_remote_get( $this->api_endpoint . 'tour?bestemming=142&per_page=100' );
        $request_category = wp_remote_get($this->api_endpoint . 'tour?tour_category=138&per_page=100');

        if ( ! is_wp_error( $request_bestemming ) && ! is_wp_error($request_category ) ) {
            $body_bestemming  = wp_remote_retrieve_body( $request_bestemming );
            $body_category = wp_remote_retrieve_body($request_category);
            $tours_destination =  json_decode( $body_bestemming );
            $tours_category = json_decode($body_category);

            $tours = array_merge($tours_category, $tours_destination);

            $unique_tours =  [];
            $ids = [];
            foreach ($tours as $tour) {
                if (!in_array($tour->id, $ids)) {
                    $ids[] = $tour->id;
                    $unique_tours[] = $tour;
                }
            }

            if (! empty($tours)) {
                return $unique_tours;
            }
        }
        return false;
    }

    public function sync_tours() {
        $tours = $this->fetch_tours();
        if (! empty($tours)) {
            foreach ($tours as $tour) {
                $tour_meta = $tour->metadata;
                insert_into_table($tour->title->rendered, $tour->id, $tour->link, $tour_meta, $this->updateMeta);
            }
        }
    }

    function fetch_tour($id)  {
        $request =  wp_remote_get( $this->api_endpoint . 'tour/' . $id );

        if ( ! is_wp_error( $request ) ) {
            $body  = wp_remote_retrieve_body( $request );
            $tour =  json_decode( $body , true  );

            if (! empty($tour)) {
                return $tour;
            }
        }
        return false;
    }
}


function sync_tours_options($options, $local_tour_id) {

    $local_tour_options = tourmaster_get_post_meta($local_tour_id, 'tourmaster-tour-option'); // get_post_meta($local_tour_id, 'tourmaster-tour-option', true);
    $date_price =  $local_tour_options['date-price'];
    $package_price =  $local_tour_options['package'];


    foreach ($date_price as $key => &$value) {
        $value['exclude-extra-date'] =  $options['exclude-extra-date'];
        $value['date'] = $options['date-price'][0]['date'];
        $value['day'] = $options['date-price'][0]['day'];
        $value['month'] = $options['date-price'][0]['month'];
        $value['year'] = $options['date-price'][0]['year'];
    }

    foreach ($package_price as $key => &$value) {
        $value['adult-price'] =  $options['adult-price-second'];
        $value['children-price'] =  $options['children-price-second'];
        $value['infant-price'] =  $options['infant-price-second'];
    }


    $local_tour_options['date-price'] = $date_price;
    $local_tour_options['package'] = $package_price;

    $local_tour_options['tour-price-text'] = $options['tour-price-text-second'];
    $local_tour_options['last-minute-booking'] = $options['last-minute-booking'];

    $local_tour_options['available_from'] = $options['available_from'];

    update_post_meta($local_tour_id, 'tourmaster-tour-option', $local_tour_options);

    // Code copy. Plugin do some process to set available dates.
    if( !empty($local_tour_options['date-price']) && !empty($local_tour_options['tour-timing-method']) ){
        $date_list = array();
        foreach( $local_tour_options['date-price'] as $settings ){
            $dates = tourmaster_get_tour_dates($settings, $local_tour_options['tour-timing-method']);
            $date_list = array_merge($date_list, $dates);
        }

        if( !empty($date_list) ){
            $date_list = array_unique($date_list);
            sort($date_list);
            update_post_meta($local_tour_id, 'tourmaster-tour-date', implode(',', $date_list));

            $book_in_advance = empty($local_tour_options['book-in-advance'])? '': $local_tour_options['book-in-advance'];
            $date_avail = tourmaster_filter_tour_date($date_list, $book_in_advance);
            if( !empty($date_avail) ){
                update_post_meta($local_tour_id, 'tourmaster-tour-date-avail', implode(',', $date_avail));
            }else{
                delete_post_meta($local_tour_id, 'tourmaster-tour-date-avail');
            }
        }else{
            delete_post_meta($local_tour_id, 'tourmaster-tour-date');
            delete_post_meta($local_tour_id, 'tourmaster-tour-date-avail');
        }
    }else{
        delete_post_meta($local_tour_id, 'tourmaster-tour-date');
        delete_post_meta($local_tour_id, 'tourmaster-tour-date-avail');
    }

}
function insert_into_table($title, $external_id, $url, $meta_data, $updateMeta = false): bool|int
{
   //
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    if ($wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE external_id = %d LIMIT 1",
            $external_id
        )
    ))
    {
        // if (is_null($meta_data)) die("test"); 
        if ($updateMeta) { update_existing_tours_metadata($external_id, $meta_data); }
        return true;
    }

    $result = $wpdb->insert(
        $table_name,
        array('title' => $title, 'external_id' => $external_id, 'url' => $url),
        array('%s', '%d', '%s')
    );

    if (!$result) {
        error_log($wpdb->last_error);
        return false;
    }

    sync_tours_names_metadata($external_id, $meta_data);
    return $wpdb->insert_id;
}

function hitCronJob() {
   error_log('Cronjob External sync started!');
   $inst = new  Custom_Posts_Sync_Plugin();
   $inst->updateMeta = true;
   $inst->sync_tours();
   error_log('Cronjob: External tours sync finished');
}

function update_existing_tours_metadata($external_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    $tours = get_posts(array(
        "post_type" => "tour",
        "tour-category" => "klein-curacao",
        "numberposts" => -1,
    ));

    $item = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE external_id = %d LIMIT 1",
            $external_id
        )
    );

    $instance = new Custom_Posts_Sync_Plugin();
    $instance->updateMeta = true;
    if ($item == null) { return false; }
    // $meta_data = (array)$meta_data;

    foreach ($tours as $tour) {
        $local_external_id = get_post_meta($tour->ID, 'external_id', true);
        if ( (int)$local_external_id === (int)$external_id) {
            $ftour = $instance->fetch_tour($item->external_id);
            sync_tours_options($ftour, $tour->ID);
            break;
        }
    }
}

function sync_tours_names_metadata($id, $meta_data) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    $tours = get_posts(array(
        "post_type" => "tour",
        "tour-category" => "klein-curacao",
        "numberposts" => -1,
    ));

    $item = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE external_id = %d LIMIT 1",
            $id
        )
    );

    if ($item == null) { return false; }

    foreach( $tours as $tour ) {
        reset_external_id($tour->ID);
        if ( similar_text($tour->title->rendered ?? $tour->title, $item->title)) {
            sync_external_id_to_meta($tour->id, $item->external_id);
            sync_tours_options($meta_data, $tour->id );
        }
    }
}

function devplus31_posts_sync_start_plugin()
{
    $instance = new Custom_Posts_Sync_Plugin();
    $instance->sync_tours();
}

function custom_data_menu() {
    add_menu_page(
        'External Tours',
        'External Tours',
        'manage_options',
        'external-tours-data',
        'external_tours_page'
    );
}

function custom_sync_cronjob() {

    if (!wp_next_scheduled( 'custom_tourmaster_sync_run_cron')) {
        wp_schedule_event(time(), 'hourly', 'custom_tourmaster_sync_run_cron');
    }
}

function custom_sync_deactivate_scheduler() {
    wp_clear_scheduled_hook( 'custom_sync_cronjob' );
}

add_action('admin_menu', 'custom_data_menu');
add_action('admin_footer', 'add_action_select_script');

register_activation_hook(__FILE__, 'custom_sync_cronjob');
register_deactivation_hook(__FILE__, 'custom_sync_deactivate_scheduler');

add_action('custom_tourmaster_sync_run_cron', function() {
   hitCronJob();
});

function add_action_select_script() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {

            $('.action-select').attr("old-local-id", $('.action-select option:selected').val() )

            $('.action-select').change(function() {
                var base = this;
                var action = $(this).val();
                var itemId = $(this).data('item-id');
                var oldLocalId = $(this).attr('old-local-id');
                var externalId = $(this).parents("tr").data("external-id");
                if (action) {
                    var url = '<?php echo admin_url('admin-ajax.php'); ?>';
                    $.post(url, {
                        action: 'perform_list_table_action',
                        list_action: action,
                        old_local_id: oldLocalId,
                        item_id: itemId,
                        external_id: externalId,
                        _ajax_nonce: '<?php echo wp_create_nonce('list_table_action'); ?>'
                    }, function(response) {
                        alert(response);

                        $(base).attr("old-local-id", $('.action-select option:selected').val() )
                        // Reset select
                        // $('.action-select').val('');
                    });
                }
            });
        });
    </script>
    <?php
}


add_action('admin_footer', 'add_custom_bulk_action_script');

function add_custom_bulk_action_script() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#remove-selected').click(function(e) {
                e.preventDefault();

                var selectedIds = [];
                $('input[name="link_posts[]"]:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                if (selectedIds.length === 0) {
                    alert('Please select items to remove.');
                    return;
                }

                if (confirm('Are you sure you want to remove the selected items?')) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'remove_selected_items',
                            post_ids: selectedIds,
                            _ajax_nonce: '<?php echo wp_create_nonce('remove_selected_items'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                selectedIds.forEach(function(id) {
                                    $('tr[data-post-id="' + id + '"]').fadeOut(400, function() {
                                        $(this).remove();
                                    });
                                });
                                alert('Selected items removed successfully.');
                                window.location.reload();
                            } else {
                                alert('Error: ' + response.data);
                            }
                        },
                        error: function() {
                            alert('An error occurred while processing your request.');
                        }
                    });
                }
            });


            $('#sync-selected').click(function(e) {
                e.preventDefault();

                var selectedIds = [];
                var selectedLocalIds = []
                $('input[name="link_posts[]"]:checked').each(function() {
                    selectedIds.push($(this).val());
                    selectedLocalIds.push(jQuery(this).parents("tr").find('.action-select option:selected').get(0).value);
                });

                if (selectedIds.length === 0) {
                    alert('Please select items to sync.');
                    return;
                }

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'sync_selected_items',
                        post_ids: selectedIds,
                        selectedIds: selectedLocalIds,
                        _ajax_nonce: '<?php echo wp_create_nonce('sync_selected_items'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            selectedIds.forEach(function(id) {
                                $('tr[data-post-id="' + id + '"]').fadeOut(400, function() {
                                    $(this).remove();
                                });
                            });
                            alert('Selected items successfully synchronized!');
                            // window.location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('An error occurred while processing your request.');
                    }
                });
            });
        });
    </script>
    <?php
}

add_action('wp_ajax_remove_selected_items', 'handle_remove_selected_items');
add_action('wp_ajax_sync_selected_items', 'handle_sync_selected_items');

function handle_sync_selected_items() {
    check_ajax_referer('sync_selected_items', '_ajax_nonce');

    if (!current_user_can('edit_posts')) {
        wp_send_json_error('You do not have permission to sync posts.');
        return;
    }

    $post_ids = isset($_POST['post_ids']) ? array_map('intval', $_POST['post_ids']) : array();
    $selectedIds = isset($_POST['selectedIds']) ? array_map('intval', $_POST['selectedIds']) : array();

    if (empty($post_ids)) {
        wp_send_json_error('No items selected.');
        return;
    }
    $instance = new Custom_Posts_Sync_Plugin();
    $sync = 0;
    foreach ($post_ids as $item_id) {
        $item = get_item_by_id($item_id);
        $tour = $instance->fetch_tour($item->external_id);
        sync_tours_options($tour, $selectedIds[$sync]);
        $sync++;
    }

    if ($sync > 0) {
        wp_send_json_success("Sync $sync item(s) successfully.");
    } else {
        wp_send_json_error('Failed to sync items.');
    }
}

function handle_remove_selected_items() {
    check_ajax_referer('remove_selected_items', '_ajax_nonce');

    if (!current_user_can('delete_posts')) {
        wp_send_json_error('You do not have permission to delete tours.');
        return;
    }

    $post_ids = isset($_POST['post_ids']) ? array_map('intval', $_POST['post_ids']) : array();

    if (empty($post_ids)) {
        wp_send_json_error('No items selected.');
        return;
    }

    $removed = 0;
    foreach ($post_ids as $post_id) {
        $result = remove_item_by_id($post_id);
        if (!is_wp_error($result)) {
            $removed++;
        }
    }

    if ($removed > 0) {
        wp_send_json_success("Removed $removed item(s) successfully.");
    } else {
        wp_send_json_error('Failed to remove items.');
    }
}

function get_item_by_id(int $item_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    $query = $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $item_id);
    $result = $wpdb->get_row($query);

    if ($result === false) {
        return new WP_Error('fetch_failed', 'Failed to fetch item.');
    }
    return $result;
}

function remove_item_by_id($item_id) {
    global $wpdb;

    $item_id = intval($item_id);
    $table_name = $wpdb->prefix . 'custom_external_posts_table';

    $query = $wpdb->prepare("DELETE FROM $table_name WHERE ID = %d", $item_id);
    $result = $wpdb->query($query);

    if ($result === false) {
        return new WP_Error('delete_failed', 'Failed to delete the item.');
    } else {
        return true;
    }
}


add_action('wp_ajax_perform_list_table_action', 'handle_list_table_action');

function handle_list_table_action() {
   //  global $instance;

    check_ajax_referer('list_table_action', '_ajax_nonce');
    $action = $_POST['action'];
    $item_id = intval($_POST['list_action']);
    $oldLocalId = intval($_POST['old_local_id']);
    $external_id = intval($_POST['external_id']);

    $instance = new Custom_Posts_Sync_Plugin();

    // Perform your action based on $action and $item_id
    switch ($action) {
        case 'perform_list_table_action':
            // Perform action 3

            if ($item_id === 0) {
                reset_external_id($oldLocalId);
                $result = "done!";
                break;
            }

            /*if ($item_id !== $oldLocalId && $oldLocalId !== 0)  {
                reset_external_id($oldLocalId);
                $result = "done!";
                break;
            }*/

            reset_external_id($oldLocalId);
            sync_external_id_to_meta($item_id, $external_id);
            $tour = $instance->fetch_tour($external_id);
            sync_tours_options($tour, $item_id);
            $result = "done!";
            break;
        default:
            $result = "Unknown action";
    }

    echo $result;
    wp_die();
}

function external_tours_page() {
    require_once(plugin_dir_path(__FILE__) . 'class/custom-data-list-table.php');
    $table = new Custom_Data_List_Table();
    $table->prepare_items();
    ?>
    <div class="wrap">
        <h1 class="wp-heading-inline">Custom Data</h1>
        <button id="remove-selected" class="button action">Remove Selected</button>
        <button id="sync-selected" class="button action">Sync Selected</button>
        <form method="post">
            <?php
            $table->display();
            ?>
        </form>

    </div>
    <?php
}


function reset_external_id($post_id) {
    update_post_meta($post_id, 'external_id', null);
}

function sync_external_id_to_meta($post_id, $external_id) {
    update_post_meta($post_id, 'external_id', $external_id);
}
function create_custom_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'custom_external_posts_table';
    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        time datetime DEFAULT now() NOT NULL,
        title tinytext NOT NULL,
        external_id mediumint(9) NOT NULL,
        url varchar(1024) DEFAULT '' NOT NULL,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
register_activation_hook(__FILE__, 'create_custom_table');


add_action("init", "devplus31_posts_sync_start_plugin");